//
//  ContentView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var speechManager = SpeechRecognitionManager()
    @State private var showingExport = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background with glass material
                Color.clear
                    .background(.regularMaterial, in: Rectangle())
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header with controls
                    headerView
                    
                    // History view as the main content
                    historyView
                }

                // Overlay for subtitle and controls at the bottom
                VStack(spacing: 0) {
                    Spacer() // Pushes content to the bottom

                    // Current transcription (subtitle)
                    currentTranscriptionView

                    // Control panel
                    controlPanelView
                }
            }
        }
        .frame(minWidth: 1000, minHeight: 700)
        .background(.windowBackground)
        .sheet(isPresented: $showingExport) {
            ExportView(transcription: speechManager.exportTranscription())
        }
        .alert("Error", isPresented: .constant(speechManager.errorMessage != nil)) {
            Button("OK") {
                speechManager.errorMessage = nil
            }
        } message: {
            Text(speechManager.errorMessage ?? "")
        }
        .onAppear {
            // Request permissions on app launch
            speechManager.requestPermissions()
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 0) {
            HStack(spacing: 16) {
                // App icon and title
                HStack(spacing: 12) {
                    Image(systemName: "waveform.badge.mic")
                        .font(.title2)
                        .foregroundStyle(.tint)
                    
                    Text("Speech Recognition")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                }
                
                Spacer()
                
                // Status indicator
                HStack(spacing: 8) {
                    Circle()
                        .fill(speechManager.isRecognizing ? .green : .secondary)
                        .frame(width: 8, height: 8)
                        .symbolEffect(.pulse, isActive: speechManager.isRecognizing)
                    
                    Text(speechManager.isRecognizing ? "Listening" : "Ready")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                }
                
                // Control buttons
                HStack(spacing: 12) {
                    Button(action: speechManager.clearHistory) {
                        Label("Clear", systemImage: "trash")
                            .labelStyle(.iconOnly)
                    }
                    .buttonStyle(.borderless)
                    .disabled(speechManager.recognizedSentences.isEmpty && speechManager.transcribedText.isEmpty)
                    
                    Button(action: { showingExport = true }) {
                        Label("Export", systemImage: "square.and.arrow.up")
                            .labelStyle(.iconOnly)
                    }
                    .buttonStyle(.borderless)
                    .disabled(speechManager.recognizedSentences.isEmpty && speechManager.transcribedText.isEmpty)
                    
                    // Permission warning
                    if !speechManager.isAuthorized {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundStyle(.orange)
                            .font(.caption)
                    }
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
            .background(.thickMaterial, in: Rectangle())
        }
    }
    
    private var historyView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                VStack(alignment: .leading) {
                    if speechManager.recognizedSentences.isEmpty {
                        // Placeholder when no history is available
                        VStack {
                            Image(systemName: "text.bubble")
                                .font(.system(size: 40))
                                .foregroundStyle(.tertiary)
                            Text("The transcript of your recognized speech will appear here.")
                                .font(.headline)
                                .foregroundStyle(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding()

                    } else {
                        // Display the full transcript as a single text block
                        Text(speechManager.recognizedSentences.joined(separator: " "))
                            .font(.system(.title3, design: .serif))
                            .lineSpacing(8)
                            .textSelection(.enabled)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .id("transcript")
                    }
                }
                .padding(.horizontal, 32)
                .padding(.top, 24)
                .onChange(of: speechManager.recognizedSentences) {
                    // Auto-scroll to the bottom of the text when new content is added
                    withAnimation {
                        proxy.scrollTo("transcript", anchor: .bottom)
                    }
                }

            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
    }
    
    private var currentTranscriptionView: some View {
        Group {
            // Only show the subtitle view if there is text or if it's actively listening.
            if !speechManager.transcribedText.isEmpty || speechManager.isRecognizing {
                Text(speechManager.transcribedText.isEmpty ? "Listening..." : speechManager.transcribedText)
                    .font(.system(size: 28, weight: .medium, design: .default))
                    .foregroundStyle(.primary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                    .frame(maxWidth: 800) // Limit width for readability
                    .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 5)
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
                    .animation(.spring(response: 0.4, dampingFraction: 0.7), value: speechManager.transcribedText)
            }

        }
        .padding(.bottom, 16) // Spacing from the control panel
    }
    
    private var controlPanelView: some View {
        HStack(spacing: 20) {
            // Main recording button
            Button(action: {
                if speechManager.isRecognizing {
                    speechManager.stopRecognition()
                } else {
                    speechManager.startRecognition()
                }
            }) {
                HStack(spacing: 10) {
                    Image(systemName: speechManager.isRecognizing ? "stop.circle.fill" : "mic.circle.fill")
                        .font(.largeTitle)
                        .symbolEffect(.pulse, isActive: speechManager.isRecognizing)
                        .foregroundStyle(speechManager.isRecognizing ? .red : .blue)
                    
                    Text(speechManager.isRecognizing ? "Stop" : "Listen")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .contentTransition(.numericText())
                }
            }
            .buttonStyle(.plain)
            .keyboardShortcut(.space, modifiers: [])
            .disabled(!speechManager.isAuthorized)
            
            Spacer()
            
            // Shortcut hint
            Text("Press Space to Start/Stop")
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 8))
        }
        .padding(.horizontal, 32)
        .padding(.vertical, 16)
        .background(.thinMaterial)
    }

    // MARK: - Helper Methods

    private func copyToClipboard(_ text: String) {
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.setString(text, forType: .string)
    }

    private func deleteSentence(at index: Int) {
        speechManager.deleteSentence(at: index)
    }
}

struct ExportView: View {
    let transcription: String
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 16) {
                Text("Export Transcription")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                ScrollView {
                    Text(transcription)
                        .font(.body)
                        .textSelection(.enabled)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 8))
                }
                
                HStack {
                    Button("Copy to Clipboard") {
                        NSPasteboard.general.clearContents()
                        NSPasteboard.general.setString(transcription, forType: .string)
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Spacer()
                    
                    Button("Close") {
                        dismiss()
                    }
                }
            }
            .padding()
            .frame(width: 500, height: 400)
        }
    }
}

#Preview {
    ContentView()
}
}

