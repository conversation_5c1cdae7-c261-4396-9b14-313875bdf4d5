//
//  realtime_speech_recognitionApp.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

@main
struct realtime_speech_recognitionApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        .commands {
            CommandGroup(replacing: .newItem) {
                <PERSON><PERSON>("Clear History") {
                    // This will need to be handled differently since we can't access ContentView state directly
                    // For now, we'll disable this feature at app level
                }
                .keyboardShortcut("k", modifiers: [.command])
                .disabled(true) // Will be enabled when we implement proper state management
                
                <PERSON><PERSON>("Export Transcription") {
                    // This will need to be handled differently since we can't access ContentView state directly
                    // For now, we'll disable this feature at app level
                }
                .keyboardShortcut("e", modifiers: [.command])
                .disabled(true) // Will be enabled when we implement proper state management
            }
        }
    }
}
