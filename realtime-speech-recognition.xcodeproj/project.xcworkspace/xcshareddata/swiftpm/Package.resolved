{"originHash": "be2b508cf91e00ccfdfd83e8177b94c9aee963c0dbf9b604f03fa19a3d85c0d4", "pins": [{"identity": "jinja", "kind": "remoteSourceControl", "location": "https://github.com/johnmai-dev/<PERSON>ja", "state": {"revision": "bb238dd96fbe4c18014f3107c00edd6edb15428e", "version": "1.2.4"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser.git", "state": {"revision": "309a47b2b1d9b5e991f36961c983ecec72275be3", "version": "1.6.1"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "8c0c0a8b49e080e54e5e328cc552821ff07cd341", "version": "1.2.1"}}, {"identity": "swift-transformers", "kind": "remoteSourceControl", "location": "https://github.com/huggingface/swift-transformers.git", "state": {"revision": "8a83416cc00ab07a5de9991e6ad817a9b8588d20", "version": "0.1.15"}}, {"identity": "whisperkit", "kind": "remoteSourceControl", "location": "https://github.com/argmaxinc/WhisperKit.git", "state": {"branch": "main", "revision": "0a064c3ba8b424887ce691c2f6c85ddffde0ce89"}}], "version": 3}